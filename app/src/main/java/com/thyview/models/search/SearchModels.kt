package com.thyview.models.search

/**
 * Unified search response model that handles multiple content types
 */
data class SearchResponse(
    val query: String,
    val type: String,
    val results: List<SearchResult>,
    val meta: SearchMeta
)

data class SearchMeta(
    val requestId: String,
    val timestamp: String
)

/**
 * Base search result that can represent movie, TV, person, or genre
 */
data class SearchResult(
    val id: Int,
    val type: String, // "movie", "tv", "person", "genre"
    
    // Movie/TV fields
    val title: String? = null,
    val name: String? = null, // For TV shows and persons
    val releaseYear: Int? = null,
    val poster: String? = null,
    val overview: String? = null,
    val runtime: Int? = null,
    val rating: Double? = null,
    val genres: List<String>? = null,
    val director: String? = null,
    val cast: List<CastMember>? = null,
    
    // TV-specific fields
    val seasons: Int? = null,
    val episodes: Int? = null,
    val status: String? = null,
    val creators: List<String>? = null,
    
    // Person-specific fields
    val profile: String? = null,
    val knownForDepartment: String? = null,
    val biography: String? = null,
    val birthday: String? = null,
    val placeOfBirth: String? = null,
    val knownFor: List<KnownForItem>? = null
)

data class CastMember(
    val id: Int,
    val name: String,
    val character: String,
    val profilePath: String?
)

data class KnownForItem(
    val id: Int,
    val title: String,
    val type: String // "movie" or "tv"
)

/**
 * Movie details response model
 */
data class MovieDetails(
    val id: Int,
    val title: String,
    val originalTitle: String,
    val tagline: String,
    val year: Int,
    val releaseDate: String,
    val rating: Double,
    val voteCount: Int,
    val popularity: Double,
    val poster: String,
    val backdrop: String,
    val overview: String,
    val runtime: Int,
    val budget: Long,
    val revenue: Long,
    val status: String,
    val adult: Boolean,
    val originalLanguage: String,
    val spokenLanguages: List<SpokenLanguage>,
    val genres: List<Genre>,
    val productionCompanies: List<ProductionCompany>,
    val productionCountries: List<ProductionCountry>,
    val cast: List<DetailedCastMember>,
    val crew: List<CrewMember>,
    val director: String,
    val directors: List<PersonSummary>,
    val writers: List<PersonSummary>,
    val producers: List<PersonSummary>,
    val similar: List<SimilarItem>,
    val recommendations: List<SimilarItem>,
    val imdbId: String,
    val homepage: String?,
    val belongsToCollection: Collection?
)

/**
 * TV details response model
 */
data class TVDetails(
    val id: Int,
    val name: String,
    val releaseYear: Int,
    val poster: String,
    val overview: String,
    val rating: Double,
    val genres: List<String>,
    val seasons: Int,
    val episodes: Int,
    val status: String,
    val creators: List<String>,
    val cast: List<CastMember>
)

/**
 * Person details response model
 */
data class PersonDetails(
    val id: Int,
    val name: String,
    val alsoKnownAs: List<String>,
    val biography: String,
    val birthday: String?,
    val deathday: String?,
    val placeOfBirth: String?,
    val knownForDepartment: String,
    val gender: Int,
    val popularity: Double,
    val adult: Boolean,
    val imdbId: String,
    val homepage: String?,
    val profile: String,
    val knownFor: List<String>,
    val movieCredits: Credits,
    val tvCredits: Credits
)

// Supporting data classes
data class SpokenLanguage(
    val code: String,
    val name: String
)

data class Genre(
    val id: Int,
    val name: String
)

data class ProductionCompany(
    val id: Int,
    val name: String,
    val logo: String?,
    val originCountry: String
)

data class ProductionCountry(
    val code: String,
    val name: String
)

data class DetailedCastMember(
    val id: Int,
    val name: String,
    val character: String,
    val creditId: String,
    val order: Int,
    val profile: String?,
    val knownForDepartment: String,
    val popularity: Double
)

data class CrewMember(
    val id: Int,
    val name: String,
    val job: String,
    val department: String,
    val creditId: String,
    val profile: String?,
    val knownForDepartment: String,
    val popularity: Double
)

data class PersonSummary(
    val id: Int,
    val name: String,
    val job: String? = null,
    val profile: String?
)

data class SimilarItem(
    val id: Int,
    val title: String,
    val year: Int,
    val poster: String,
    val rating: Double,
    val overview: String
)

data class Collection(
    val id: Int,
    val name: String,
    val poster: String,
    val backdrop: String
)

data class Credits(
    val cast: List<CreditItem>,
    val crew: List<CreditItem>
)

data class CreditItem(
    val id: Int,
    val title: String? = null,
    val name: String? = null, // For TV shows
    val character: String? = null,
    val job: String? = null,
    val department: String? = null,
    val releaseDate: String? = null,
    val firstAirDate: String? = null,
    val year: Int?,
    val poster: String?,
    val rating: Double,
    val popularity: Double,
    val order: Int? = null,
    val creditId: String,
    val episodeCount: Int? = null // For TV credits
)

/**
 * Watchlist and streaming service models
 */
data class StreamingService(
    val id: Int,
    val name: String,
    val icon: String,
    val url: String? = null
)

data class WatchlistResponse(
    val success: Boolean,
    val message: String,
    val isInWatchlist: Boolean
)
