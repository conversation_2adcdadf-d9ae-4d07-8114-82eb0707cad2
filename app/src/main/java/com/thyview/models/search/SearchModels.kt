package com.thyview.models.search

import com.google.gson.annotations.SerializedName

/**
 * Unified search response model that handles multiple content types
 */
data class SearchResponse(
    val query: String,
    val type: String,
    val results: List<SearchResult>,
    val meta: SearchMeta
)

data class SearchMeta(
    val requestId: String,
    val timestamp: String
)

/**
 * Base search result that can represent movie, TV, person, or genre
 */
data class SearchResult(
    val id: Int,
    val type: String, // "movie", "tv", "person", "genre"
    
    // Movie/TV fields
    val title: String? = null,
    val name: String? = null, // For TV shows and persons
    val releaseYear: Int? = null,
    val poster: String? = null,
    val overview: String? = null,
    val runtime: Int? = null,
    val rating: Double? = null,
    val genres: List<String>? = null,
    val director: String? = null,
    val cast: List<CastMember>? = null,
    
    // TV-specific fields
    val seasons: Int? = null,
    val episodes: Int? = null,
    val status: String? = null,
    val creators: List<String>? = null,
    
    // Person-specific fields
    val profile: String? = null,
    val knownForDepartment: String? = null,
    val biography: String? = null,
    val birthday: String? = null,
    val placeOfBirth: String? = null,
    val knownFor: List<KnownForItem>? = null
)

data class CastMember(
    val id: Int,
    val name: String,
    val character: String,
    val profilePath: String?
)

data class KnownForItem(
    val id: Int,
    val title: String,
    val type: String // "movie" or "tv"
)

/**
 * Movie details response model
 */
data class MovieDetails(
    val id: Int,
    val title: String,
    val originalTitle: String,
    val tagline: String,
    val year: Int,
    val releaseDate: String,
    val rating: Double,
    val voteCount: Int,
    val popularity: Double,
    val poster: String,
    val backdrop: String,
    val overview: String,
    val runtime: Int,
    val budget: Long,
    val revenue: Long,
    val status: String,
    val adult: Boolean,
    val originalLanguage: String,
    val spokenLanguages: List<SpokenLanguage>,
    val genres: List<Genre>,
    val productionCompanies: List<ProductionCompany>,
    val productionCountries: List<ProductionCountry>,
    val cast: List<DetailedCastMember>,
    val crew: List<CrewMember>,
    val director: String,
    val directors: List<PersonSummary>,
    val writers: List<PersonSummary>,
    val producers: List<PersonSummary>,
    val similar: List<SimilarItem>,
    val recommendations: List<SimilarItem>,
    val imdbId: String,
    val homepage: String?,
    val belongsToCollection: Collection?
)

/**
 * TV details response model
 */
data class TVDetails(
    val id: Int,
    val name: String,
    @SerializedName("original_name") val originalName: String? = null,
    val tagline: String? = null,
    val year: Int,
    @SerializedName("first_air_date") val firstAirDate: String? = null,
    @SerializedName("last_air_date") val lastAirDate: String? = null,
    val rating: Double,
    @SerializedName("vote_count") val voteCount: Int? = null,
    val popularity: Double? = null,
    val poster: String,
    val backdrop: String? = null,
    val overview: String,
    val status: String,
    val type: String? = null,
    val adult: Boolean? = null,
    @SerializedName("in_production") val inProduction: Boolean? = null,
    @SerializedName("original_language") val originalLanguage: String? = null,
    @SerializedName("origin_country") val originCountry: List<String>? = null,
    @SerializedName("spoken_languages") val spokenLanguages: List<SpokenLanguage>? = null,
    val seasons: Int,
    val episodes: Int,
    @SerializedName("episode_run_time") val episodeRunTime: List<Int>? = null,
    @SerializedName("season_details") val seasonDetails: List<SeasonDetail>? = null,
    val genres: List<Genre>,
    val networks: List<Network>? = null,
    @SerializedName("production_companies") val productionCompanies: List<ProductionCompany>? = null,
    @SerializedName("production_countries") val productionCountries: List<ProductionCountry>? = null,
    val cast: List<DetailedCastMember>,
    val crew: List<CrewMember>? = null,
    val creators: List<String>? = null,
    val similar: List<SimilarItem>? = null,
    val recommendations: List<SimilarItem>? = null,
    val homepage: String? = null,
    val languages: List<String>? = null,
    @SerializedName("next_episode_to_air") val nextEpisodeToAir: Episode? = null,
    @SerializedName("last_episode_to_air") val lastEpisodeToAir: Episode? = null
) {
    // Computed property for backward compatibility
    val releaseYear: Int get() = year
}

/**
 * Person details response model
 */
data class PersonDetails(
    val id: Int,
    val name: String,
    val alsoKnownAs: List<String>,
    val biography: String,
    val birthday: String?,
    val deathday: String?,
    val placeOfBirth: String?,
    val knownForDepartment: String,
    val gender: Int,
    val popularity: Double,
    val adult: Boolean,
    val imdbId: String,
    val homepage: String?,
    val profile: String,
    val knownFor: List<String>,
    val movieCredits: Credits,
    val tvCredits: Credits
)

// Supporting data classes
data class SpokenLanguage(
    val code: String,
    val name: String
)

data class Genre(
    val id: Int,
    val name: String
)

data class ProductionCompany(
    val id: Int,
    val name: String,
    val logo: String?,
    @SerializedName("origin_country") val originCountry: String
)

data class ProductionCountry(
    val code: String,
    val name: String
)

data class DetailedCastMember(
    val id: Int,
    val name: String,
    val character: String,
    @SerializedName("credit_id") val creditId: String,
    val order: Int,
    val profile: String?,
    @SerializedName("known_for_department") val knownForDepartment: String,
    val popularity: Double
)

data class CrewMember(
    val id: Int,
    val name: String,
    val job: String,
    val department: String,
    val creditId: String,
    val profile: String?,
    val knownForDepartment: String,
    val popularity: Double
)

data class PersonSummary(
    val id: Int,
    val name: String,
    val job: String? = null,
    val profile: String?
)

data class SimilarItem(
    val id: Int,
    val title: String,
    val year: Int,
    val poster: String,
    val rating: Double,
    val overview: String
)

data class Collection(
    val id: Int,
    val name: String,
    val poster: String,
    val backdrop: String
)

data class Credits(
    val cast: List<CreditItem>,
    val crew: List<CreditItem>
)

data class CreditItem(
    val id: Int,
    val title: String? = null,
    val name: String? = null, // For TV shows
    val character: String? = null,
    val job: String? = null,
    val department: String? = null,
    val releaseDate: String? = null,
    val firstAirDate: String? = null,
    val year: Int?,
    val poster: String?,
    val rating: Double,
    val popularity: Double,
    val order: Int? = null,
    val creditId: String,
    val episodeCount: Int? = null // For TV credits
)

/**
 * Watchlist and streaming service models
 */
data class StreamingService(
    val id: Int,
    val name: String,
    val icon: String,
    val url: String? = null
)

data class WatchlistResponse(
    val success: Boolean,
    val message: String,
    val isInWatchlist: Boolean
)

// TV-specific data classes
data class SeasonDetail(
    val id: Int,
    @SerializedName("season_number") val seasonNumber: Int,
    val name: String,
    val overview: String,
    @SerializedName("air_date") val airDate: String?,
    @SerializedName("episode_count") val episodeCount: Int,
    val poster: String?
)

data class Network(
    val id: Int,
    val name: String,
    val logo: String?,
    @SerializedName("origin_country") val originCountry: String
)

data class Episode(
    val id: Int,
    val name: String,
    val overview: String,
    @SerializedName("air_date") val airDate: String,
    @SerializedName("episode_number") val episodeNumber: Int,
    @SerializedName("season_number") val seasonNumber: Int
)
